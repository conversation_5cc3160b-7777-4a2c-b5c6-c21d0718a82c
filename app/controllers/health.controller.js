const os = require('os');
const fs = require('fs');
const path = require('path');

/**
 * Get comprehensive health status of the Node.js application
 */
exports.getHealthStatus = async (req, res) => {
  try {
    const startTime = process.hrtime.bigint();
    
    // Runtime Metrics
    const runtimeMetrics = getRuntimeMetrics();
    
    // Application Metrics
    const applicationMetrics = getApplicationMetrics();
    
    // System Metrics
    const systemMetrics = getSystemMetrics();
    
    // Database Health (basic check)
    const databaseHealth = await getDatabaseHealth();
    
    // Event Loop Metrics
    const eventLoopMetrics = await getEventLoopMetrics();
    
    // Process Information
    const processInfo = getProcessInfo();
    
    // Disk Usage
    const diskUsage = getDiskUsage();
    
    // Network Information
    const networkInfo = getNetworkInfo();
    
    const endTime = process.hrtime.bigint();
    const responseTime = Number(endTime - startTime) / 1000000; // Convert to milliseconds
    
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      responseTime: `${responseTime.toFixed(2)}ms`,
      uptime: {
        process: formatUptime(process.uptime()),
        system: formatUptime(os.uptime())
      },
      runtime: runtimeMetrics,
      application: applicationMetrics,
      system: systemMetrics,
      database: databaseHealth,
      eventLoop: eventLoopMetrics,
      process: processInfo,
      disk: diskUsage,
      network: networkInfo,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        nodeEnv: process.env.NODE_ENV || 'development'
      }
    };

    res.status(200).json(healthData);
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      uptime: {
        process: formatUptime(process.uptime()),
        system: formatUptime(os.uptime())
      }
    });
  }
};

/**
 * Get runtime metrics (memory, CPU, GC)
 */
function getRuntimeMetrics() {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  return {
    memory: {
      rss: {
        bytes: memUsage.rss,
        mb: Math.round(memUsage.rss / 1024 / 1024 * 100) / 100,
        description: 'Resident Set Size - total memory allocated for the process'
      },
      heapTotal: {
        bytes: memUsage.heapTotal,
        mb: Math.round(memUsage.heapTotal / 1024 / 1024 * 100) / 100,
        description: 'Total heap allocated'
      },
      heapUsed: {
        bytes: memUsage.heapUsed,
        mb: Math.round(memUsage.heapUsed / 1024 / 1024 * 100) / 100,
        percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
        description: 'Heap actually used'
      },
      external: {
        bytes: memUsage.external,
        mb: Math.round(memUsage.external / 1024 / 1024 * 100) / 100,
        description: 'Memory used by C++ objects bound to JavaScript objects'
      },
      arrayBuffers: {
        bytes: memUsage.arrayBuffers || 0,
        mb: Math.round((memUsage.arrayBuffers || 0) / 1024 / 1024 * 100) / 100,
        description: 'Memory allocated for ArrayBuffers and SharedArrayBuffers'
      }
    },
    cpu: {
      user: Math.round(cpuUsage.user / 1000), // Convert to milliseconds
      system: Math.round(cpuUsage.system / 1000),
      description: 'CPU time used by the process (user and system time in ms)'
    }
  };
}

/**
 * Get application-level metrics
 */
function getApplicationMetrics() {
  return {
    pid: process.pid,
    ppid: process.ppid,
    title: process.title,
    argv: process.argv.slice(2), // Remove node and script path
    execPath: process.execPath,
    cwd: process.cwd(),
    versions: process.versions
  };
}

/**
 * Get system metrics
 */
function getSystemMetrics() {
  const loadAvg = os.loadavg();
  const cpus = os.cpus();
  const totalMem = os.totalmem();
  const freeMem = os.freemem();
  const usedMem = totalMem - freeMem;
  
  return {
    hostname: os.hostname(),
    platform: os.platform(),
    arch: os.arch(),
    release: os.release(),
    type: os.type(),
    loadAverage: {
      '1min': Math.round(loadAvg[0] * 100) / 100,
      '5min': Math.round(loadAvg[1] * 100) / 100,
      '15min': Math.round(loadAvg[2] * 100) / 100,
      description: 'System load average'
    },
    cpu: {
      count: cpus.length,
      model: cpus[0]?.model || 'Unknown',
      speed: cpus[0]?.speed || 0,
      usage: calculateCpuUsage(cpus)
    },
    memory: {
      total: {
        bytes: totalMem,
        gb: Math.round(totalMem / 1024 / 1024 / 1024 * 100) / 100
      },
      free: {
        bytes: freeMem,
        gb: Math.round(freeMem / 1024 / 1024 / 1024 * 100) / 100
      },
      used: {
        bytes: usedMem,
        gb: Math.round(usedMem / 1024 / 1024 / 1024 * 100) / 100,
        percentage: Math.round((usedMem / totalMem) * 100)
      }
    }
  };
}

/**
 * Get database health status
 */
async function getDatabaseHealth() {
  try {
    // Basic database connection check
    const mongoose = require('mongoose');
    const connectionState = mongoose.connection.readyState;
    
    const states = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };
    
    return {
      status: connectionState === 1 ? 'healthy' : 'unhealthy',
      state: states[connectionState] || 'unknown',
      host: mongoose.connection.host || 'unknown',
      name: mongoose.connection.name || 'unknown',
      collections: mongoose.connection.db ? Object.keys(mongoose.connection.db.collections || {}).length : 0
    };
  } catch (error) {
    return {
      status: 'error',
      error: error.message
    };
  }
}

/**
 * Get event loop metrics
 */
function getEventLoopMetrics() {
  const startTime = process.hrtime.bigint();
  
  return new Promise((resolve) => {
    setImmediate(() => {
      const endTime = process.hrtime.bigint();
      const lag = Number(endTime - startTime) / 1000000; // Convert to milliseconds
      
      resolve({
        lag: {
          ms: Math.round(lag * 100) / 100,
          status: lag > 100 ? 'warning' : lag > 500 ? 'critical' : 'healthy',
          description: 'Event loop lag in milliseconds'
        }
      });
    });
  });
}

/**
 * Get process information
 */
function getProcessInfo() {
  return {
    pid: process.pid,
    ppid: process.ppid,
    uid: process.getuid ? process.getuid() : null,
    gid: process.getgid ? process.getgid() : null,
    title: process.title,
    uptime: process.uptime(),
    resourceUsage: process.resourceUsage ? process.resourceUsage() : null
  };
}

/**
 * Get disk usage information
 */
function getDiskUsage() {
  try {
    const stats = fs.statSync(process.cwd());
    return {
      currentDirectory: process.cwd(),
      accessible: true,
      stats: {
        dev: stats.dev,
        ino: stats.ino,
        mode: stats.mode,
        nlink: stats.nlink,
        uid: stats.uid,
        gid: stats.gid,
        size: stats.size,
        atime: stats.atime,
        mtime: stats.mtime,
        ctime: stats.ctime
      }
    };
  } catch (error) {
    return {
      currentDirectory: process.cwd(),
      accessible: false,
      error: error.message
    };
  }
}

/**
 * Get network information
 */
function getNetworkInfo() {
  const networkInterfaces = os.networkInterfaces();
  const interfaces = {};
  
  for (const [name, addresses] of Object.entries(networkInterfaces)) {
    interfaces[name] = addresses.map(addr => ({
      address: addr.address,
      netmask: addr.netmask,
      family: addr.family,
      mac: addr.mac,
      internal: addr.internal,
      cidr: addr.cidr
    }));
  }
  
  return {
    interfaces,
    hostname: os.hostname()
  };
}

/**
 * Calculate CPU usage percentage
 */
function calculateCpuUsage(cpus) {
  let totalIdle = 0;
  let totalTick = 0;
  
  cpus.forEach(cpu => {
    for (const type in cpu.times) {
      totalTick += cpu.times[type];
    }
    totalIdle += cpu.times.idle;
  });
  
  const idle = totalIdle / cpus.length;
  const total = totalTick / cpus.length;
  const usage = 100 - ~~(100 * idle / total);
  
  return {
    percentage: usage,
    status: usage > 80 ? 'critical' : usage > 60 ? 'warning' : 'healthy'
  };
}

/**
 * Format uptime in human readable format
 */
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  return {
    raw: seconds,
    formatted: `${days}d ${hours}h ${minutes}m ${secs}s`,
    days,
    hours,
    minutes,
    seconds: secs
  };
}
