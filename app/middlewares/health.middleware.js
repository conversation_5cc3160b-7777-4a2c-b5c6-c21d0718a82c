const crypto = require('crypto');
const rateLimit = require('express-rate-limit');

/**
 * Rate limiter middleware using express-rate-limit - allows 2 requests per minute per IP
 */
exports.rateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 2, // limit each IP to 2 requests per windowMs
  message: {
    status: false,
    message: 'Too many requests. Health endpoint allows 2 requests per minute.',
    retryAfter: 60,
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  handler: (req, res) => {
    res.status(429).json({
      status: false,
      message: 'Too many requests. Health endpoint allows 2 requests per minute.',
      retryAfter: Math.ceil(req.rateLimit.resetTime / 1000),
    });
  },
});

/**
 * Basic authentication middleware for health endpoint
 * Uses environment variables for credentials
 */
exports.basicAuth = (req, res, next) => {
  // Get credentials from environment variables
  const healthUsername = process.env.HEALTH_USERNAME || 'admin';
  const healthPassword = process.env.HEALTH_PASSWORD || 'health@123';

  // Get authorization header
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Basic ')) {
    res.set('WWW-Authenticate', 'Basic realm="Health Endpoint"');
    return res.status(401).json({
      status: false,
      message: 'Authentication required. Use Basic Auth with username and password.',
      hint: 'Authorization: Basic <base64(username:password)>',
    });
  }

  try {
    // Decode base64 credentials
    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
    const [username, password] = credentials.split(':');

    // Verify credentials using constant-time comparison to prevent timing attacks
    const usernameMatch = crypto.timingSafeEqual(
      Buffer.from(username),
      Buffer.from(healthUsername)
    );
    const passwordMatch = crypto.timingSafeEqual(
      Buffer.from(password),
      Buffer.from(healthPassword)
    );

    if (usernameMatch && passwordMatch) {
      next();
    } else {
      res.set('WWW-Authenticate', 'Basic realm="Health Endpoint"');
      return res.status(401).json({
        status: false,
        message: 'Invalid credentials',
      });
    }
  } catch (error) {
    res.set('WWW-Authenticate', 'Basic realm="Health Endpoint"');
    return res.status(401).json({
      status: false,
      message: 'Invalid authorization header format',
    });
  }
};
