const crypto = require('crypto');

// In-memory store for rate limiting (in production, use Redis)
const rateLimitStore = new Map();

/**
 * Rate limiter middleware - allows 2 requests per minute per IP
 */
exports.rateLimiter = (req, res, next) => {
  const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress;
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute
  const maxRequests = 2;

  // Clean up old entries
  for (const [ip, data] of rateLimitStore.entries()) {
    if (now - data.resetTime > windowMs) {
      rateLimitStore.delete(ip);
    }
  }

  // Get or create rate limit data for this IP
  let rateLimitData = rateLimitStore.get(clientIP);
  
  if (!rateLimitData) {
    rateLimitData = {
      count: 0,
      resetTime: now
    };
    rateLimitStore.set(clientIP, rateLimitData);
  }

  // Reset count if window has passed
  if (now - rateLimitData.resetTime > windowMs) {
    rateLimitData.count = 0;
    rateLimitData.resetTime = now;
  }

  // Check if limit exceeded
  if (rateLimitData.count >= maxRequests) {
    return res.status(429).json({
      status: false,
      message: 'Too many requests. Health endpoint allows 2 requests per minute.',
      retryAfter: Math.ceil((windowMs - (now - rateLimitData.resetTime)) / 1000)
    });
  }

  // Increment count
  rateLimitData.count++;

  // Add rate limit headers
  res.set({
    'X-RateLimit-Limit': maxRequests,
    'X-RateLimit-Remaining': Math.max(0, maxRequests - rateLimitData.count),
    'X-RateLimit-Reset': new Date(rateLimitData.resetTime + windowMs).toISOString()
  });

  next();
};

/**
 * Basic authentication middleware for health endpoint
 * Uses environment variables for credentials
 */
exports.basicAuth = (req, res, next) => {
  // Get credentials from environment variables
  const healthUsername = process.env.HEALTH_USERNAME || 'admin';
  const healthPassword = process.env.HEALTH_PASSWORD || 'health@123';

  // Get authorization header
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Basic ')) {
    res.set('WWW-Authenticate', 'Basic realm="Health Endpoint"');
    return res.status(401).json({
      status: false,
      message: 'Authentication required. Use Basic Auth with username and password.',
      hint: 'Authorization: Basic <base64(username:password)>'
    });
  }

  try {
    // Decode base64 credentials
    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
    const [username, password] = credentials.split(':');

    // Verify credentials using constant-time comparison to prevent timing attacks
    const usernameMatch = crypto.timingSafeEqual(
      Buffer.from(username),
      Buffer.from(healthUsername)
    );
    const passwordMatch = crypto.timingSafeEqual(
      Buffer.from(password),
      Buffer.from(healthPassword)
    );

    if (usernameMatch && passwordMatch) {
      next();
    } else {
      res.set('WWW-Authenticate', 'Basic realm="Health Endpoint"');
      return res.status(401).json({
        status: false,
        message: 'Invalid credentials'
      });
    }
  } catch (error) {
    res.set('WWW-Authenticate', 'Basic realm="Health Endpoint"');
    return res.status(401).json({
      status: false,
      message: 'Invalid authorization header format'
    });
  }
};
