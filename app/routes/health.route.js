// library
const express = require('express');
const routes = express.Router();

// controller
const healthController = require('../controllers/health.controller');

// middleware
const { rateLimiter, basicAuth } = require('../middlewares/health.middleware');

// Health endpoint with rate limiting and basic auth
routes.get('', rateLimiter, basicAuth, healthController.getHealthStatus);

module.exports = routes;
