# Enhanced Health Endpoint Documentation

## Overview
The `/api/health` endpoint provides production-grade monitoring information about your Node.js application, including runtime metrics, system information, database status, performance indicators, and intelligent alerting. This enhanced version follows observability best practices with structured data, status indicators, and security measures.

## 🚀 Enhanced Features

### ✅ Production-Grade Improvements
- **Structured Response**: Clean, semantically grouped sections (memory, cpu, disk, network, database)
- **Status Indicators**: Each section includes health status (healthy/warning/critical)
- **Intelligent Alerts**: Automatic threshold monitoring with actionable alerts
- **Build Information**: Version, git commit, and build timestamp tracking
- **Production Sanitization**: Sensitive information masked in production environments
- **Enhanced CPU Metrics**: Core count, threads, per-core usage, and load averages
- **Memory Optimization**: Detailed heap analysis with usage percentages and status
- **Event Loop Monitoring**: Lag detection with status indicators

### 🔒 Security Enhancements
- **Production Sanitization**: Hides sensitive paths, IPs, and system details in production
- **Environment-Aware**: Different detail levels based on NODE_ENV
- **Secure Authentication**: Constant-time comparison prevents timing attacks

## Security Features

### 1. Rate Limiting (Enhanced with express-rate-limit)
- **Limit**: 2 requests per minute per IP address
- **Implementation**: Uses `express-rate-limit` package for robust rate limiting
- **Response Headers**:
  - `RateLimit-Limit`: Maximum requests allowed
  - `RateLimit-Remaining`: Remaining requests in current window
  - `RateLimit-Reset`: When the rate limit resets
- **Rate Limit Exceeded**: Returns HTTP 429 with retry information
- **Production Ready**: Automatic cleanup and memory efficient

### 2. Basic Authentication
- **Method**: HTTP Basic Authentication
- **Credentials**: Set via environment variables
  - `HEALTH_USERNAME` (default: "admin")
  - `HEALTH_PASSWORD` (default: "health@123")
- **Security**: Uses constant-time comparison to prevent timing attacks

## Usage

### Environment Variables
Add these to your `.env` file:
```env
HEALTH_USERNAME=devteam
HEALTH_PASSWORD=health@reynard2024
```

### Making Requests

#### Using curl:
```bash
curl -u devteam:health@reynard2024 http://localhost:8000/api/health
```

#### Using curl with explicit headers:
```bash
curl -H "Authorization: Basic ZGV2dGVhbTpoZWFsdGhAcmV5bmFyZDIwMjQ=" http://localhost:8000/api/health
```

#### Using JavaScript/Node.js:
```javascript
const response = await fetch('http://localhost:8000/api/health', {
  headers: {
    'Authorization': 'Basic ' + Buffer.from('devteam:health@reynard2024').toString('base64')
  }
});
const healthData = await response.json();
```

## Response Structure

### Enhanced Response Structure (HTTP 200)
```json
{
  "status": "warning",
  "timestamp": "2025-06-19T12:07:08.226Z",
  "responseTime": "136.67ms",
  "uptime": {
    "process": {
      "raw": 43.003648387,
      "formatted": "0d 0h 0m 43s",
      "days": 0,
      "hours": 0,
      "minutes": 0,
      "seconds": 43
    },
    "system": {
      "raw": 25317.54,
      "formatted": "0d 7h 1m 57s",
      "days": 0,
      "hours": 7,
      "minutes": 1,
      "seconds": 57
    }
  },
  "version": "1.0.1",
  "gitCommit": "6361886a",
  "buildTimestamp": "2025-06-19T11:15:45.000Z",
  "memory": {
    "processMemory": {
      "rss": {
        "bytes": 149286912,
        "mb": 142.37
      },
      "heapTotal": {
        "bytes": 77262848,
        "mb": 73.68
      },
      "heapUsed": {
        "bytes": 72224048,
        "mb": 68.88
      },
      "heapUsagePercent": 93,
      "external": {
        "bytes": 22277011,
        "mb": 21.25
      },
      "status": "warning"
    },
    "systemMemory": {
      "total": {
        "bytes": 16047992832,
        "gb": 14.95
      },
      "used": {
        "bytes": 12204933120,
        "gb": 11.37
      },
      "free": {
        "bytes": **********,
        "gb": 3.58
      },
      "usagePercent": 76,
      "status": "warning"
    }
  },
  "cpu": {
    "processCPU": {
      "user": 2489,
      "system": 227,
      "total": 2716
    },
    "systemCPU": {
      "loadAverage": {
        "1min": 2.7,
        "5min": 2.08,
        "15min": 1.5
      },
      "usagePercent": 9,
      "status": "healthy"
    },
    "cores": {
      "count": 12,
      "model": "AMD Ryzen 5 5500U with Radeon Graphics",
      "speed": 1807,
      "threads": 12
    }
  },
  "disk": {
    "currentDirectory": "/home/<USER>/app",
    "accessible": true,
    "status": "healthy",
    "lastModified": "2025-06-19T11:34:28.809Z"
  },
  "network": {
    "hostname": "server-hostname",
    "interfaceCount": 3,
    "publicIPs": ["************", "************"],
    "status": "healthy"
  },
  "database": {
    "mongodb": {
      "status": "healthy",
      "state": "connected",
      "host": "127.0.0.1",
      "name": "reynard-prod",
      "collections": 0,
      "readyState": 1
    },
    "status": "healthy"
  },
  "eventLoop": {
    "lagMs": 0.13,
    "status": "healthy"
  },
  "environment": {
    "nodeVersion": "v22.14.0",
    "platform": "linux",
    "arch": "x64",
    "nodeEnv": "local"
  },
  "alerts": [
    {
      "level": "warning",
      "message": "Heap memory usage high (>80%)"
    }
  ]
}
```

## Error Responses

### Authentication Required (HTTP 401)
```json
{
  "status": false,
  "message": "Authentication required. Use Basic Auth with username and password.",
  "hint": "Authorization: Basic <base64(username:password)>"
}
```

### Invalid Credentials (HTTP 401)
```json
{
  "status": false,
  "message": "Invalid credentials"
}
```

### Rate Limit Exceeded (HTTP 429)
```json
{
  "status": false,
  "message": "Too many requests. Health endpoint allows 2 requests per minute.",
  "retryAfter": 45
}
```

### Server Error (HTTP 500)
```json
{
  "status": "unhealthy",
  "timestamp": "2025-06-19T11:29:41.274Z",
  "error": "Error message",
  "uptime": {
    "process": {
      "raw": 23.774108701,
      "formatted": "0d 0h 0m 23s"
    },
    "system": {
      "raw": 23070.59,
      "formatted": "0d 6h 24m 30s"
    }
  }
}
```

## Monitoring Thresholds

### Memory Usage
- **Warning**: Heap usage > 80%
- **Critical**: Heap usage > 95%
- **Memory Leak**: Continuous growth in RSS without returning to baseline

### CPU Usage
- **Healthy**: < 60%
- **Warning**: 60-80%
- **Critical**: > 80%

### Event Loop Lag
- **Healthy**: < 100ms
- **Warning**: 100-500ms
- **Critical**: > 500ms

### Load Average
- **Healthy**: < CPU core count
- **Warning**: 1-2x CPU core count
- **Critical**: > 2x CPU core count

## Implementation Details

### Built with Core Node.js Modules
- `os` - System information
- `fs` - File system access
- `crypto` - Secure authentication
- `process` - Process metrics
- No external dependencies required

### Security Considerations
- Rate limiting prevents abuse
- Basic authentication restricts access
- Constant-time comparison prevents timing attacks
- No sensitive information exposed in responses
- IP-based rate limiting with automatic cleanup

### Performance Impact
- Minimal overhead (~100-200ms response time)
- Efficient memory usage tracking
- Non-blocking event loop lag measurement
- Cached system information where possible

## Integration with Monitoring Tools

This endpoint can be integrated with:
- **Prometheus**: Scrape metrics for alerting
- **Grafana**: Create dashboards
- **Nagios/Zabbix**: Health checks
- **Load Balancers**: Health probes
- **Custom monitoring scripts**: Automated checks

## Best Practices

1. **Monitor regularly** but respect rate limits
2. **Set up alerts** based on the thresholds above
3. **Use HTTPS** in production
4. **Rotate credentials** periodically
5. **Monitor trends** rather than single data points
6. **Correlate metrics** for better insights
