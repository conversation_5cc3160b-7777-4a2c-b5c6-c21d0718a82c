# Health Endpoint Documentation

## Overview
The `/api/health` endpoint provides comprehensive monitoring information about your Node.js application, including runtime metrics, system information, database status, and performance indicators.

## Security Features

### 1. Rate Limiting
- **Limit**: 2 requests per minute per IP address
- **Response Headers**: 
  - `X-RateLimit-Limit`: Maximum requests allowed
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: When the rate limit resets
- **Rate Limit Exceeded**: Returns HTTP 429 with retry information

### 2. Basic Authentication
- **Method**: HTTP Basic Authentication
- **Credentials**: Set via environment variables
  - `HEALTH_USERNAME` (default: "admin")
  - `HEALTH_PASSWORD` (default: "health@123")
- **Security**: Uses constant-time comparison to prevent timing attacks

## Usage

### Environment Variables
Add these to your `.env` file:
```env
HEALTH_USERNAME=devteam
HEALTH_PASSWORD=health@reynard2024
```

### Making Requests

#### Using curl:
```bash
curl -u devteam:health@reynard2024 http://localhost:8000/api/health
```

#### Using curl with explicit headers:
```bash
curl -H "Authorization: Basic ZGV2dGVhbTpoZWFsdGhAcmV5bmFyZDIwMjQ=" http://localhost:8000/api/health
```

#### Using JavaScript/Node.js:
```javascript
const response = await fetch('http://localhost:8000/api/health', {
  headers: {
    'Authorization': 'Basic ' + Buffer.from('devteam:health@reynard2024').toString('base64')
  }
});
const healthData = await response.json();
```

## Response Structure

### Successful Response (HTTP 200)
```json
{
  "status": "healthy",
  "timestamp": "2025-06-19T11:29:41.274Z",
  "responseTime": "174.43ms",
  "uptime": {
    "process": {
      "raw": 23.774108701,
      "formatted": "0d 0h 0m 23s",
      "days": 0,
      "hours": 0,
      "minutes": 0,
      "seconds": 23
    },
    "system": {
      "raw": 23070.59,
      "formatted": "0d 6h 24m 30s",
      "days": 0,
      "hours": 6,
      "minutes": 24,
      "seconds": 30
    }
  },
  "runtime": {
    "memory": {
      "rss": {
        "bytes": 147898368,
        "mb": 141.05,
        "description": "Resident Set Size - total memory allocated for the process"
      },
      "heapTotal": {
        "bytes": 75952128,
        "mb": 72.43,
        "description": "Total heap allocated"
      },
      "heapUsed": {
        "bytes": 71522544,
        "mb": 68.21,
        "percentage": 94,
        "description": "Heap actually used"
      },
      "external": {
        "bytes": 22262811,
        "mb": 21.23,
        "description": "Memory used by C++ objects bound to JavaScript objects"
      },
      "arrayBuffers": {
        "bytes": 18831125,
        "mb": 17.96,
        "description": "Memory allocated for ArrayBuffers and SharedArrayBuffers"
      }
    },
    "cpu": {
      "user": 2162,
      "system": 269,
      "description": "CPU time used by the process (user and system time in ms)"
    }
  },
  "system": {
    "hostname": "server-hostname",
    "platform": "linux",
    "arch": "x64",
    "loadAverage": {
      "1min": 1.83,
      "5min": 1.33,
      "15min": 1.05,
      "description": "System load average"
    },
    "cpu": {
      "count": 12,
      "model": "AMD Ryzen 5 5500U with Radeon Graphics",
      "speed": 1428,
      "usage": {
        "percentage": 9,
        "status": "healthy"
      }
    },
    "memory": {
      "total": {
        "bytes": 16047992832,
        "gb": 14.95
      },
      "free": {
        "bytes": **********,
        "gb": 4.36
      },
      "used": {
        "bytes": 11365568512,
        "gb": 10.59,
        "percentage": 71
      }
    }
  },
  "database": {
    "status": "healthy",
    "state": "connected",
    "host": "127.0.0.1",
    "name": "reynard-prod",
    "collections": 0
  },
  "eventLoop": {
    "lag": {
      "ms": 0.2,
      "status": "healthy",
      "description": "Event loop lag in milliseconds"
    }
  },
  "process": {
    "pid": 62558,
    "ppid": 62557,
    "uid": 1001,
    "gid": 1001,
    "title": "node",
    "uptime": 23.77224431,
    "resourceUsage": {
      "userCPUTime": 2168284,
      "systemCPUTime": 268776,
      "maxRSS": 179480,
      "sharedMemorySize": 0,
      "unsharedDataSize": 0,
      "unsharedStackSize": 0,
      "minorPageFault": 37239,
      "majorPageFault": 1,
      "swappedOut": 0,
      "fsRead": 992,
      "fsWrite": 0,
      "ipcSent": 0,
      "ipcReceived": 0,
      "signalsCount": 0,
      "voluntaryContextSwitches": 1691,
      "involuntaryContextSwitches": 282
    }
  },
  "disk": {
    "currentDirectory": "/path/to/app",
    "accessible": true,
    "stats": {
      "dev": 66306,
      "ino": 4326211,
      "mode": 16893,
      "nlink": 15,
      "uid": 1001,
      "gid": 1001,
      "size": 4096,
      "atime": "2025-06-19T07:00:47.805Z",
      "mtime": "2025-06-19T07:00:45.721Z",
      "ctime": "2025-06-19T07:00:45.721Z"
    }
  },
  "network": {
    "interfaces": {
      "lo": [
        {
          "address": "127.0.0.1",
          "netmask": "*********",
          "family": "IPv4",
          "mac": "00:00:00:00:00:00",
          "internal": true,
          "cidr": "127.0.0.1/8"
        }
      ]
    },
    "hostname": "server-hostname"
  },
  "environment": {
    "nodeVersion": "v22.14.0",
    "platform": "linux",
    "arch": "x64",
    "nodeEnv": "local"
  }
}
```

## Error Responses

### Authentication Required (HTTP 401)
```json
{
  "status": false,
  "message": "Authentication required. Use Basic Auth with username and password.",
  "hint": "Authorization: Basic <base64(username:password)>"
}
```

### Invalid Credentials (HTTP 401)
```json
{
  "status": false,
  "message": "Invalid credentials"
}
```

### Rate Limit Exceeded (HTTP 429)
```json
{
  "status": false,
  "message": "Too many requests. Health endpoint allows 2 requests per minute.",
  "retryAfter": 45
}
```

### Server Error (HTTP 500)
```json
{
  "status": "unhealthy",
  "timestamp": "2025-06-19T11:29:41.274Z",
  "error": "Error message",
  "uptime": {
    "process": {
      "raw": 23.774108701,
      "formatted": "0d 0h 0m 23s"
    },
    "system": {
      "raw": 23070.59,
      "formatted": "0d 6h 24m 30s"
    }
  }
}
```

## Monitoring Thresholds

### Memory Usage
- **Warning**: Heap usage > 80%
- **Critical**: Heap usage > 95%
- **Memory Leak**: Continuous growth in RSS without returning to baseline

### CPU Usage
- **Healthy**: < 60%
- **Warning**: 60-80%
- **Critical**: > 80%

### Event Loop Lag
- **Healthy**: < 100ms
- **Warning**: 100-500ms
- **Critical**: > 500ms

### Load Average
- **Healthy**: < CPU core count
- **Warning**: 1-2x CPU core count
- **Critical**: > 2x CPU core count

## Implementation Details

### Built with Core Node.js Modules
- `os` - System information
- `fs` - File system access
- `crypto` - Secure authentication
- `process` - Process metrics
- No external dependencies required

### Security Considerations
- Rate limiting prevents abuse
- Basic authentication restricts access
- Constant-time comparison prevents timing attacks
- No sensitive information exposed in responses
- IP-based rate limiting with automatic cleanup

### Performance Impact
- Minimal overhead (~100-200ms response time)
- Efficient memory usage tracking
- Non-blocking event loop lag measurement
- Cached system information where possible

## Integration with Monitoring Tools

This endpoint can be integrated with:
- **Prometheus**: Scrape metrics for alerting
- **Grafana**: Create dashboards
- **Nagios/Zabbix**: Health checks
- **Load Balancers**: Health probes
- **Custom monitoring scripts**: Automated checks

## Best Practices

1. **Monitor regularly** but respect rate limits
2. **Set up alerts** based on the thresholds above
3. **Use HTTPS** in production
4. **Rotate credentials** periodically
5. **Monitor trends** rather than single data points
6. **Correlate metrics** for better insights
